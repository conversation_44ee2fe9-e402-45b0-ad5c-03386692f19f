        private static bool ColumnExists(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            const string sql = @"SELECT 1
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = @schemaName AND TABLE_NAME = @tableName AND COLUMN_NAME = @columnName;";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);
                command.Parameters.AddWithValue("@columnName", columnName);

                using (var reader = command.ExecuteReader())
                {
                    return reader.Read();
                }
            }
        }

        private static void EnsurePrimaryKey(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            if (HasPrimaryKey(connection, schemaName, tableName))
            {
                throw new InvalidOperationException($"Table '{tableName}' already has a primary key. Modify the existing key manually if changes are required.");
            }

            var constraintName = BuildConstraintName($"PK_{tableName}");
            var sql = $"ALTER TABLE {ComposeFullTableName(schemaName, tableName)} ADD CONSTRAINT {EscapeIdentifier(constraintName)} PRIMARY KEY ({EscapeIdentifier(columnName)});";

            using (var command = new SqlCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static bool HasPrimaryKey(SqlConnection connection, string schemaName, string tableName)
        {
            const string sql = @"SELECT 1
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_SCHEMA = @schemaName AND TABLE_NAME = @tableName AND CONSTRAINT_TYPE = 'PRIMARY KEY';";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);

                using (var reader = command.ExecuteReader())
                {
                    return reader.Read();
                }
            }
        }

        private static void ApplyDefaultConstraint(SqlConnection connection, string schemaName, string tableName, string columnName, string defaultValue)
        {
            var constraintName = BuildConstraintName($"DF_{tableName}_{columnName}");
            var sql = $"ALTER TABLE {ComposeFullTableName(schemaName, tableName)} ADD CONSTRAINT {EscapeIdentifier(constraintName)} DEFAULT ({defaultValue}) FOR {EscapeIdentifier(columnName)};";

            using (var command = new SqlCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static void DropDefaultConstraint(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            const string sql = @"SELECT dc.name
FROM sys.default_constraints dc
JOIN sys.columns c ON c.default_object_id = dc.object_id
JOIN sys.tables t ON t.object_id = c.object_id
JOIN sys.schemas s ON s.schema_id = t.schema_id
WHERE s.name = @schemaName AND t.name = @tableName AND c.name = @columnName;";

            string constraintName = null;
            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);
                command.Parameters.AddWithValue("@columnName", columnName);

                var result = command.ExecuteScalar();
                if (result != null && result != DBNull.Value)
                {
                    constraintName = Convert.ToString(result);
                }
            }

            if (string.IsNullOrWhiteSpace(constraintName))
            {
                return;
            }

            var dropSql = $"ALTER TABLE {ComposeFullTableName(schemaName, tableName)} DROP CONSTRAINT {EscapeIdentifier(constraintName)};";

            using (var command = new SqlCommand(dropSql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static ColumnInfo GetColumnInfo(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            const string sql = @"SELECT
    c.name AS ColumnName,
    ty.name AS DataType,
    c.max_length,
    c.precision,
    c.scale,
    c.is_nullable,
    c.is_identity,
    CASE WHEN i.is_primary_key = 1 THEN 1 ELSE 0 END AS IsPrimaryKey,
    dc.definition AS DefaultValue
FROM sys.columns c
JOIN sys.tables t ON c.object_id = t.object_id
JOIN sys.schemas s ON t.schema_id = s.schema_id
JOIN sys.types ty ON c.user_type_id = ty.user_type_id
LEFT JOIN sys.index_columns ic ON ic.object_id = c.object_id AND ic.column_id = c.column_id
LEFT JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id AND i.is_primary_key = 1
LEFT JOIN sys.default_constraints dc ON c.default_object_id = dc.object_id
WHERE s.name = @schemaName AND t.name = @tableName AND c.name = @columnName;";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);
                command.Parameters.AddWithValue("@columnName", columnName);

                using (var reader = command.ExecuteReader(CommandBehavior.SingleRow))
                {
                    if (!reader.Read())
                    {
                        return null;
                    }

                    return ReadColumnInfo(reader);
                }
            }
        }

        private static IReadOnlyList<ColumnInfo> LoadColumns(clData data, string tableName)
        {
            using (var connection = data.GetConnection())
            {
                var schemaName = GetTableSchema(connection, tableName);
                var result = new List<ColumnInfo>();

                const string sql = @"SELECT
    c.name AS ColumnName,
    ty.name AS DataType,
    c.max_length,
    c.precision,
    c.scale,
    c.is_nullable,
    c.is_identity,
    CASE WHEN i.is_primary_key = 1 THEN 1 ELSE 0 END AS IsPrimaryKey,
    dc.definition AS DefaultValue
FROM sys.columns c
JOIN sys.tables t ON c.object_id = t.object_id
JOIN sys.schemas s ON t.schema_id = s.schema_id
JOIN sys.types ty ON c.user_type_id = ty.user_type_id
LEFT JOIN sys.index_columns ic ON ic.object_id = c.object_id AND ic.column_id = c.column_id
LEFT JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id AND i.is_primary_key = 1
LEFT JOIN sys.default_constraints dc ON c.default_object_id = dc.object_id
WHERE s.name = @schemaName AND t.name = @tableName
ORDER BY c.column_id;";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@schemaName", schemaName);
                    command.Parameters.AddWithValue("@tableName", tableName);

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.Add(ReadColumnInfo(reader));
                        }
                    }
                }

                return result.Count == 0 ? (IReadOnlyList<ColumnInfo>)Array.Empty<ColumnInfo>() : result.AsReadOnly();
            }
        }

        private static ColumnInfo ReadColumnInfo(SqlDataReader reader)
        {
            var dataType = reader.GetString(reader.GetOrdinal("DataType"));
            var maxLengthOrdinal = reader.GetOrdinal("max_length");
            var precisionOrdinal = reader.GetOrdinal("precision");
            var scaleOrdinal = reader.GetOrdinal("scale");
            var defaultOrdinal = reader.GetOrdinal("DefaultValue");

            var rawLength = reader.IsDBNull(maxLengthOrdinal) ? (int?)null : Convert.ToInt32(reader.GetValue(maxLengthOrdinal));
            var rawPrecision = reader.IsDBNull(precisionOrdinal) ? (int?)null : Convert.ToInt32(reader.GetValue(precisionOrdinal));
            var rawScale = reader.IsDBNull(scaleOrdinal) ? (int?)null : Convert.ToInt32(reader.GetValue(scaleOrdinal));
            var defaultValue = reader.IsDBNull(defaultOrdinal) ? null : reader.GetString(defaultOrdinal);

            return new ColumnInfo
            {
                Name = reader.GetString(reader.GetOrdinal("ColumnName")),
                DataType = dataType,
                Length = ConvertMaxLength(dataType, rawLength),
                Precision = rawPrecision,
                Scale = rawScale,
                IsNullable = reader.GetBoolean(reader.GetOrdinal("is_nullable")),
                IsIdentity = reader.GetBoolean(reader.GetOrdinal("is_identity")),
                IsPrimaryKey = reader.GetInt32(reader.GetOrdinal("IsPrimaryKey")) == 1,
                DefaultValue = NormalizeDefaultValue(defaultValue)
            };
        }

        private static int? ConvertMaxLength(string dataType, int? rawLength)
        {
            if (!rawLength.HasValue)
            {
                return null;
            }

            if (rawLength.Value == -1)
            {
                return -1;
            }

            switch (dataType.ToLowerInvariant())
            {
                case "nchar":
                case "nvarchar":
                    return rawLength.Value / 2;
                default:
                    return rawLength;
            }
        }

        private static string NormalizeDefaultValue(string definition)
        {
            if (string.IsNullOrWhiteSpace(definition))
            {
                return null;
            }

            var value = definition.Trim();
            while (value.StartsWith("(", StringComparison.Ordinal) && value.EndsWith(")", StringComparison.Ordinal) && value.Length > 1)
            {
                value = value.Substring(1, value.Length - 2).Trim();
            }

            return value;
        }

        private static bool IsIdentityType(string dataType)
        {
            if (string.IsNullOrWhiteSpace(dataType))
            {
                return false;
            }

            var normalized = dataType.Trim().ToLowerInvariant();
            return normalized == "int" || normalized == "bigint" || normalized == "decimal" || normalized == "numeric";
        }

        private static string BuildColumnDefinition(TableColumnInput column, bool isNullable, bool includeIdentity)
        {
            var sqlType = BuildSqlType(column.DataType, column.Length, column.Precision, column.Scale);
            var builder = new StringBuilder(sqlType);

            if (includeIdentity)
            {
                builder.Append(" IDENTITY(1,1)");
            }

            builder.Append(isNullable ? " NULL" : " NOT NULL");

            return builder.ToString();
        }

        private static string BuildColumnDefinition(string dataType, int? length, int? precision, int? scale, bool isNullable, bool includeIdentity)
        {
            var sqlType = BuildSqlType(dataType, length, precision, scale);
            var builder = new StringBuilder(sqlType);

            if (includeIdentity)
            {
                builder.Append(" IDENTITY(1,1)");
            }

            builder.Append(isNullable ? " NULL" : " NOT NULL");

            return builder.ToString();
        }

        private static string BuildSqlType(string dataType, int? length, int? precision, int? scale)
        {
            if (string.IsNullOrWhiteSpace(dataType))
            {
                throw new InvalidOperationException("Column data type is required.");
            }

            var normalized = dataType.Trim().ToLowerInvariant();
            switch (normalized)
            {
                case "char":
                case "nchar":
                case "varchar":
                case "nvarchar":
                case "binary":
                case "varbinary":
                    if (!length.HasValue)
                    {
                        throw new InvalidOperationException($"Column length is required for data type '{normalized}'.");
                    }

                    var effectiveLength = length.Value == -1 ? "MAX" : length.Value.ToString();
                    return $"{normalized.ToUpperInvariant()}({effectiveLength})";
                case "decimal":
                case "numeric":
                    var effectivePrecision = precision ?? throw new InvalidOperationException($"Precision is required for data type '{normalized}'.");
                    var effectiveScale = scale ?? 0;
                    return $"{normalized.ToUpperInvariant()}({effectivePrecision},{effectiveScale})";
                case "datetime2":
                case "datetimeoffset":
                case "time":
                    if (scale.HasValue)
                    {
                        return $"{normalized.ToUpperInvariant()}({scale.Value})";
                    }

                    break;
            }

            return normalized.ToUpperInvariant();
        }

        private static void ValidateColumnDefinition(TableColumnInput column, bool requireDataType)
        {
            if (requireDataType && string.IsNullOrWhiteSpace(column.DataType))
            {
                throw new InvalidOperationException($"Column '{column.Name}' must specify a data type.");
            }
        }

        private static string BuildCreateTableCommand(TableMetadata metadata)
        {
            var name = EscapeSqlLiteral(metadata.Name);
            var label = EscapeSqlLiteral(metadata.Label);
            var labelPlural = EscapeSqlLiteral(metadata.LabelPlural);

            var createFileLabels = metadata.CreateFileLabels ? 1 : 0;
            var createLinks = metadata.CreateLinks ? 1 : 0;
            var createFieldLabels = metadata.CreateFieldLabels ? 1 : 0;
            var createForm = metadata.CreateForm ? 1 : 0;
            var createDesktop = metadata.CreateDesktop ? 1 : 0;
            var createPermissions = metadata.CreatePermissions ? 1 : 0;

            return $"exec pCreateTable @par_sTableName='{name}',@par_sTableLabel='{label}',@par_sTableLabelPlural='{labelPlural}'," +
                   $"@par_bCreateFileLabels={createFileLabels},@par_bCreateLinks={createLinks},@par_bCreateFieldLabels={createFieldLabels}," +
                   $"@par_bCreateForm={createForm},@par_bCreateDesktop={createDesktop},@par_bCreatePermissions={createPermissions},@par_bVerbose=0";
        }

        private static void EnsureLabelUniqueness(IEnumerable<TableDescriptor> existingTables, TableMetadata metadata)
        {
            if (existingTables.Any(table => string.Equals(table.Label, metadata.Label, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"Table label '{metadata.Label}' already exists.");
            }

            if (existingTables.Any(table => string.Equals(table.LabelPlural, metadata.LabelPlural, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"Table plural label '{metadata.LabelPlural}' already exists.");
            }
        }

        private static string NormalizeIdentifier(string value, string fieldName)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                throw new ArgumentException($"{fieldName} is required.", fieldName);
            }

            var trimmed = value.Trim();
            if (!IdentifierRegex.IsMatch(trimmed))
            {
                throw new ArgumentException($"{fieldName} must contain only letters, numbers, or underscores.", fieldName);
            }

            return trimmed;
        }

        private static string EscapeSqlLiteral(string value)
        {
            return (value ?? string.Empty).Replace("'", "''");
        }

        private static string EscapeIdentifier(string identifier)
        {
            return "[" + identifier.Replace("]", "]]" ) + "]";
        }

        private static string ComposeFullTableName(string schemaName, string tableName)
        {
            return $"{EscapeIdentifier(schemaName)}.{EscapeIdentifier(tableName)}";
        }

        private static string BuildConstraintName(string baseName)
        {
            var sanitized = IdentifierRegex.IsMatch(baseName) ? baseName : new string(baseName.Where(char.IsLetterOrDigit).ToArray());
            if (sanitized.Length == 0)
            {
                sanitized = "DF_Auto";
            }

            return sanitized.Length <= 120 ? sanitized : sanitized.Substring(0, 120);
        }

        private static clData ResolveLegacyDataContext()
        {
            var data = Util.GetInstance("data") as clData;
            if (data == null)
            {
                throw new InvalidOperationException("Selltis legacy data context could not be resolved.");
            }

            return data;
        }

        private static IEnumerable<TableDescriptor> LoadSelltisTables(clData data, bool includeLabels)
        {
            var results = new List<TableDescriptor>();
            var files = data.GetFiles(false);
            if (files == null)
            {
                return results;
            }

            for (int index = 1; index <= files.GetDimension(); index++)
            {
                var tableName = files.GetItem(index);
                var descriptor = new TableDescriptor
                {
                    Name = tableName
                };

                if (includeLabels)
                {
                    descriptor.Label = data.GetFileLabelFromName(tableName);
                    descriptor.LabelPlural = data.GetFileLabelPlurFrName(tableName);
                }

                results.Add(descriptor);
            }

            return results;
        }

        private static TableInfo BuildTableInfo(clData data, string hostName, string tableName, bool includeColumns)
        {
            var label = data.GetFileLabelFromName(tableName);
            var labelPlural = data.GetFileLabelPlurFrName(tableName);

            IReadOnlyList<ColumnInfo> columns = Array.Empty<ColumnInfo>();
            if (includeColumns)
            {
                columns = LoadColumns(data, tableName);
            }

            return new TableInfo
            {
                HostName = hostName,
                Name = tableName,
                Label = label,
                LabelPlural = string.IsNullOrWhiteSpace(labelPlural) ? label : labelPlural,
                Columns = columns
            };
        }

        private static string GetTableSchema(SqlConnection connection, string tableName)
        {
            const string sql = @"SELECT TOP 1 TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @tableName;";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@tableName", tableName);

                var result = command.ExecuteScalar();
                return result as string ?? DefaultSchemaName;
            }
        }

        private sealed class TableDescriptor
        {
            public string Name { get; set; }
            public string Label { get; set; }
            public string LabelPlural { get; set; }
        }
    }
}



        private static bool ColumnExists(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            const string sql = @"SELECT 1
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = @schemaName AND TABLE_NAME = @tableName AND COLUMN_NAME = @columnName;";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);
                command.Parameters.AddWithValue("@columnName", columnName);

                using (var reader = command.ExecuteReader())
                {
                    return reader.Read();
                }
            }
        }

        private static void EnsurePrimaryKey(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            if (HasPrimaryKey(connection, schemaName, tableName))
            {
                throw new InvalidOperationException($"Table '{tableName}' already has a primary key. Modify the existing key manually if changes are required.");
            }

            var constraintName = BuildConstraintName($"PK_{tableName}");
            var sql = $"ALTER TABLE {ComposeFullTableName(schemaName, tableName)} ADD CONSTRAINT {EscapeIdentifier(constraintName)} PRIMARY KEY ({EscapeIdentifier(columnName)});";

            using (var command = new SqlCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static bool HasPrimaryKey(SqlConnection connection, string schemaName, string tableName)
        {
            const string sql = @"SELECT 1
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_SCHEMA = @schemaName AND TABLE_NAME = @tableName AND CONSTRAINT_TYPE = 'PRIMARY KEY';";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);

                using (var reader = command.ExecuteReader())
                {
                    return reader.Read();
                }
            }
        }

        private static void ApplyDefaultConstraint(SqlConnection connection, string schemaName, string tableName, string columnName, string defaultValue)
        {
            var constraintName = BuildConstraintName($"DF_{tableName}_{columnName}");
            var sql = $"ALTER TABLE {ComposeFullTableName(schemaName, tableName)} ADD CONSTRAINT {EscapeIdentifier(constraintName)} DEFAULT ({defaultValue}) FOR {EscapeIdentifier(columnName)};";

            using (var command = new SqlCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static void DropDefaultConstraint(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            const string sql = @"SELECT dc.name
FROM sys.default_constraints dc
JOIN sys.columns c ON c.default_object_id = dc.object_id
JOIN sys.tables t ON t.object_id = c.object_id
JOIN sys.schemas s ON s.schema_id = t.schema_id
WHERE s.name = @schemaName AND t.name = @tableName AND c.name = @columnName;";

            string constraintName = null;
            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);
                command.Parameters.AddWithValue("@columnName", columnName);

                var result = command.ExecuteScalar();
                if (result != null && result != DBNull.Value)
                {
                    constraintName = Convert.ToString(result);
                }
            }

            if (string.IsNullOrWhiteSpace(constraintName))
            {
                return;
            }

            var dropSql = $"ALTER TABLE {ComposeFullTableName(schemaName, tableName)} DROP CONSTRAINT {EscapeIdentifier(constraintName)};";

            using (var command = new SqlCommand(dropSql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static ColumnInfo GetColumnInfo(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            const string sql = @"SELECT
    c.name AS ColumnName,
    ty.name AS DataType,
    c.max_length,
    c.precision,
    c.scale,
    c.is_nullable,
    c.is_identity,
    CASE WHEN i.is_primary_key = 1 THEN 1 ELSE 0 END AS IsPrimaryKey,
    dc.definition AS DefaultValue
FROM sys.columns c
JOIN sys.tables t ON c.object_id = t.object_id
JOIN sys.schemas s ON t.schema_id = s.schema_id
JOIN sys.types ty ON c.user_type_id = ty.user_type_id
LEFT JOIN sys.index_columns ic ON ic.object_id = c.object_id AND ic.column_id = c.column_id
LEFT JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id AND i.is_primary_key = 1
LEFT JOIN sys.default_constraints dc ON c.default_object_id = dc.object_id
WHERE s.name = @schemaName AND t.name = @tableName AND c.name = @columnName;";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);
                command.Parameters.AddWithValue("@columnName", columnName);

                using (var reader = command.ExecuteReader(CommandBehavior.SingleRow))
                {
                    if (!reader.Read())
                    {
                        return null;
                    }

                    return ReadColumnInfo(reader);
                }
            }
        }

        private static IReadOnlyList<ColumnInfo> LoadColumns(clData data, string tableName)
        {
            using (var connection = data.GetConnection())
            {
                var schemaName = GetTableSchema(connection, tableName);
                var result = new List<ColumnInfo>();

                const string sql = @"SELECT
    c.name AS ColumnName,
    ty.name AS DataType,
    c.max_length,
    c.precision,
    c.scale,
    c.is_nullable,
    c.is_identity,
    CASE WHEN i.is_primary_key = 1 THEN 1 ELSE 0 END AS IsPrimaryKey,
    dc.definition AS DefaultValue
FROM sys.columns c
JOIN sys.tables t ON c.object_id = t.object_id
JOIN sys.schemas s ON t.schema_id = s.schema_id
JOIN sys.types ty ON c.user_type_id = ty.user_type_id
LEFT JOIN sys.index_columns ic ON ic.object_id = c.object_id AND ic.column_id = c.column_id
LEFT JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id AND i.is_primary_key = 1
LEFT JOIN sys.default_constraints dc ON c.default_object_id = dc.object_id
WHERE s.name = @schemaName AND t.name = @tableName
ORDER BY c.column_id;";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@schemaName", schemaName);
                    command.Parameters.AddWithValue("@tableName", tableName);

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.Add(ReadColumnInfo(reader));
                        }
                    }
                }

                return result.Count == 0 ? (IReadOnlyList<ColumnInfo>)Array.Empty<ColumnInfo>() : result.AsReadOnly();
            }
        }

        private static ColumnInfo ReadColumnInfo(SqlDataReader reader)
        {
            var dataType = reader.GetString(reader.GetOrdinal("DataType"));
            var maxLengthOrdinal = reader.GetOrdinal("max_length");
            var precisionOrdinal = reader.GetOrdinal("precision");
            var scaleOrdinal = reader.GetOrdinal("scale");
            var defaultOrdinal = reader.GetOrdinal("DefaultValue");

            var rawLength = reader.IsDBNull(maxLengthOrdinal) ? (int?)null : Convert.ToInt32(reader.GetValue(maxLengthOrdinal));
            var rawPrecision = reader.IsDBNull(precisionOrdinal) ? (int?)null : Convert.ToInt32(reader.GetValue(precisionOrdinal));
            var rawScale = reader.IsDBNull(scaleOrdinal) ? (int?)null : Convert.ToInt32(reader.GetValue(scaleOrdinal));
            var defaultValue = reader.IsDBNull(defaultOrdinal) ? null : reader.GetString(defaultOrdinal);

            return new ColumnInfo
            {
                Name = reader.GetString(reader.GetOrdinal("ColumnName")),
                DataType = dataType,
                Length = ConvertMaxLength(dataType, rawLength),
                Precision = rawPrecision,
                Scale = rawScale,
                IsNullable = reader.GetBoolean(reader.GetOrdinal("is_nullable")),
                IsIdentity = reader.GetBoolean(reader.GetOrdinal("is_identity")),
                IsPrimaryKey = reader.GetInt32(reader.GetOrdinal("IsPrimaryKey")) == 1,
                DefaultValue = NormalizeDefaultValue(defaultValue)
            };
        }

        private static int? ConvertMaxLength(string dataType, int? rawLength)
        {
            if (!rawLength.HasValue)
            {
                return null;
            }

            if (rawLength.Value == -1)
            {
                return -1;
            }

            switch (dataType.ToLowerInvariant())
            {
                case "nchar":
                case "nvarchar":
                    return rawLength.Value / 2;
                default:
                    return rawLength;
            }
        }

        private static string NormalizeDefaultValue(string definition)
        {
            if (string.IsNullOrWhiteSpace(definition))
            {
                return null;
            }

            var value = definition.Trim();
            while (value.StartsWith("(", StringComparison.Ordinal) && value.EndsWith(")", StringComparison.Ordinal) && value.Length > 1)
            {
                value = value.Substring(1, value.Length - 2).Trim();
            }

            return value;
        }

        private static bool IsIdentityType(string dataType)
        {
            if (string.IsNullOrWhiteSpace(dataType))
            {
                return false;
            }

            var normalized = dataType.Trim().ToLowerInvariant();
            return normalized == "int" || normalized == "bigint" || normalized == "decimal" || normalized == "numeric";
        }

        private static string BuildColumnDefinition(TableColumnInput column, bool isNullable, bool includeIdentity)
        {
            var sqlType = BuildSqlType(column.DataType, column.Length, column.Precision, column.Scale);
            var builder = new StringBuilder(sqlType);

            if (includeIdentity)
            {
                builder.Append(" IDENTITY(1,1)");
            }

            builder.Append(isNullable ? " NULL" : " NOT NULL");

            return builder.ToString();
        }

        private static string BuildColumnDefinition(string dataType, int? length, int? precision, int? scale, bool isNullable, bool includeIdentity)
        {
            var sqlType = BuildSqlType(dataType, length, precision, scale);
            var builder = new StringBuilder(sqlType);

            if (includeIdentity)
            {
                builder.Append(" IDENTITY(1,1)");
            }

            builder.Append(isNullable ? " NULL" : " NOT NULL");

            return builder.ToString();
        }

        private static string BuildSqlType(string dataType, int? length, int? precision, int? scale)
        {
            if (string.IsNullOrWhiteSpace(dataType))
            {
                throw new InvalidOperationException("Column data type is required.");
            }

            var normalized = dataType.Trim().ToLowerInvariant();
            switch (normalized)
            {
                case "char":
                case "nchar":
                case "varchar":
                case "nvarchar":
                case "binary":
                case "varbinary":
                    if (!length.HasValue)
                    {
                        throw new InvalidOperationException($"Column length is required for data type '{normalized}'.");
                    }

                    var effectiveLength = length.Value == -1 ? "MAX" : length.Value.ToString();
                    return $"{normalized.ToUpperInvariant()}({effectiveLength})";
                case "decimal":
                case "numeric":
                    var effectivePrecision = precision ?? throw new InvalidOperationException($"Precision is required for data type '{normalized}'.");
                    var effectiveScale = scale ?? 0;
                    return $"{normalized.ToUpperInvariant()}({effectivePrecision},{effectiveScale})";
                case "datetime2":
                case "datetimeoffset":
                case "time":
                    if (scale.HasValue)
                    {
                        return $"{normalized.ToUpperInvariant()}({scale.Value})";
                    }

                    break;
            }

            return normalized.ToUpperInvariant();
        }

        private static void ValidateColumnDefinition(TableColumnInput column, bool requireDataType)
        {
            if (requireDataType && string.IsNullOrWhiteSpace(column.DataType))
            {
                throw new InvalidOperationException($"Column '{column.Name}' must specify a data type.");
            }
        }

        private static string BuildCreateTableCommand(TableMetadata metadata)
        {
            var name = EscapeSqlLiteral(metadata.Name);
            var label = EscapeSqlLiteral(metadata.Label);
            var labelPlural = EscapeSqlLiteral(metadata.LabelPlural);

            var createFileLabels = metadata.CreateFileLabels ? 1 : 0;
            var createLinks = metadata.CreateLinks ? 1 : 0;
            var createFieldLabels = metadata.CreateFieldLabels ? 1 : 0;
            var createForm = metadata.CreateForm ? 1 : 0;
            var createDesktop = metadata.CreateDesktop ? 1 : 0;
            var createPermissions = metadata.CreatePermissions ? 1 : 0;

            return $"exec pCreateTable @par_sTableName='{name}',@par_sTableLabel='{label}',@par_sTableLabelPlural='{labelPlural}'," +
                   $"@par_bCreateFileLabels={createFileLabels},@par_bCreateLinks={createLinks},@par_bCreateFieldLabels={createFieldLabels}," +
                   $"@par_bCreateForm={createForm},@par_bCreateDesktop={createDesktop},@par_bCreatePermissions={createPermissions},@par_bVerbose=0";
        }

        private static void EnsureLabelUniqueness(IEnumerable<TableDescriptor> existingTables, TableMetadata metadata)
        {
            if (existingTables.Any(table => string.Equals(table.Label, metadata.Label, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"Table label '{metadata.Label}' already exists.");
            }

            if (existingTables.Any(table => string.Equals(table.LabelPlural, metadata.LabelPlural, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"Table plural label '{metadata.LabelPlural}' already exists.");
            }
        }

        private static string NormalizeIdentifier(string value, string fieldName)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                throw new ArgumentException($"{fieldName} is required.", fieldName);
            }

            var trimmed = value.Trim();
            if (!IdentifierRegex.IsMatch(trimmed))
            {
                throw new ArgumentException($"{fieldName} must contain only letters, numbers, or underscores.", fieldName);
            }

            return trimmed;
        }

        private static string EscapeSqlLiteral(string value)
        {
            return (value ?? string.Empty).Replace("'", "''");
        }

        private static string EscapeIdentifier(string identifier)
        {
            return "[" + identifier.Replace("]", "]]" ) + "]";
        }

        private static string ComposeFullTableName(string schemaName, string tableName)
        {
            return $"{EscapeIdentifier(schemaName)}.{EscapeIdentifier(tableName)}";
        }

        private static string BuildConstraintName(string baseName)
        {
            var sanitized = IdentifierRegex.IsMatch(baseName) ? baseName : new string(baseName.Where(char.IsLetterOrDigit).ToArray());
            if (sanitized.Length == 0)
            {
                sanitized = "DF_Auto";
            }

            return sanitized.Length <= 120 ? sanitized : sanitized.Substring(0, 120);
        }

        private static clData ResolveLegacyDataContext()
        {
            var data = Util.GetInstance("data") as clData;
            if (data == null)
            {
                throw new InvalidOperationException("Selltis legacy data context could not be resolved.");
            }

            return data;
        }

        private static IEnumerable<TableDescriptor> LoadSelltisTables(clData data, bool includeLabels)
        {
            var results = new List<TableDescriptor>();
            var files = data.GetFiles(false);
            if (files == null)
            {
                return results;
            }

            for (int index = 1; index <= files.GetDimension(); index++)
            {
                var tableName = files.GetItem(index);
                var descriptor = new TableDescriptor
                {
                    Name = tableName
                };

                if (includeLabels)
                {
                    descriptor.Label = data.GetFileLabelFromName(tableName);
                    descriptor.LabelPlural = data.GetFileLabelPlurFrName(tableName);
                }

                results.Add(descriptor);
            }

            return results;
        }

        private static TableInfo BuildTableInfo(clData data, string hostName, string tableName, bool includeColumns)
        {
            var label = data.GetFileLabelFromName(tableName);
            var labelPlural = data.GetFileLabelPlurFrName(tableName);

            IReadOnlyList<ColumnInfo> columns = Array.Empty<ColumnInfo>();
            if (includeColumns)
            {
                columns = LoadColumns(data, tableName);
            }

            return new TableInfo
            {
                HostName = hostName,
                Name = tableName,
                Label = label,
                LabelPlural = string.IsNullOrWhiteSpace(labelPlural) ? label : labelPlural,
                Columns = columns
            };
        }

        private static string GetTableSchema(SqlConnection connection, string tableName)
        {
            const string sql = @"SELECT TOP 1 TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @tableName;";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@tableName", tableName);

                var result = command.ExecuteScalar();
                return result as string ?? DefaultSchemaName;
            }
        }

        private sealed class TableDescriptor
        {
            public string Name { get; set; }
            public string Label { get; set; }
            public string LabelPlural { get; set; }
        }
    }
}
        private static bool ColumnExists(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            const string sql = @"SELECT 1
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = @schemaName AND TABLE_NAME = @tableName AND COLUMN_NAME = @columnName;";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);
                command.Parameters.AddWithValue("@columnName", columnName);

                using (var reader = command.ExecuteReader())
                {
                    return reader.Read();
                }
            }
        }

        private static void EnsurePrimaryKey(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            if (HasPrimaryKey(connection, schemaName, tableName))
            {
                throw new InvalidOperationException($"Table '{tableName}' already has a primary key. Modify the existing key manually if changes are required.");
            }

            var constraintName = BuildConstraintName($"PK_{tableName}");
            var sql = $"ALTER TABLE {ComposeFullTableName(schemaName, tableName)} ADD CONSTRAINT {EscapeIdentifier(constraintName)} PRIMARY KEY ({EscapeIdentifier(columnName)});";

            using (var command = new SqlCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static bool HasPrimaryKey(SqlConnection connection, string schemaName, string tableName)
        {
            const string sql = @"SELECT 1
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_SCHEMA = @schemaName AND TABLE_NAME = @tableName AND CONSTRAINT_TYPE = 'PRIMARY KEY';";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);

                using (var reader = command.ExecuteReader())
                {
                    return reader.Read();
                }
            }
        }

        private static void ApplyDefaultConstraint(SqlConnection connection, string schemaName, string tableName, string columnName, string defaultValue)
        {
            var constraintName = BuildConstraintName($"DF_{tableName}_{columnName}");
            var sql = $"ALTER TABLE {ComposeFullTableName(schemaName, tableName)} ADD CONSTRAINT {EscapeIdentifier(constraintName)} DEFAULT ({defaultValue}) FOR {EscapeIdentifier(columnName)};";

            using (var command = new SqlCommand(sql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static void DropDefaultConstraint(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            const string sql = @"SELECT dc.name
FROM sys.default_constraints dc
JOIN sys.columns c ON c.default_object_id = dc.object_id
JOIN sys.tables t ON t.object_id = c.object_id
JOIN sys.schemas s ON s.schema_id = t.schema_id
WHERE s.name = @schemaName AND t.name = @tableName AND c.name = @columnName;";

            string constraintName = null;
            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);
                command.Parameters.AddWithValue("@columnName", columnName);

                var result = command.ExecuteScalar();
                if (result != null && result != DBNull.Value)
                {
                    constraintName = Convert.ToString(result);
                }
            }

            if (string.IsNullOrWhiteSpace(constraintName))
            {
                return;
            }

            var dropSql = $"ALTER TABLE {ComposeFullTableName(schemaName, tableName)} DROP CONSTRAINT {EscapeIdentifier(constraintName)};";

            using (var command = new SqlCommand(dropSql, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static ColumnInfo GetColumnInfo(SqlConnection connection, string schemaName, string tableName, string columnName)
        {
            const string sql = @"SELECT
    c.name AS ColumnName,
    ty.name AS DataType,
    c.max_length,
    c.precision,
    c.scale,
    c.is_nullable,
    c.is_identity,
    CASE WHEN i.is_primary_key = 1 THEN 1 ELSE 0 END AS IsPrimaryKey,
    dc.definition AS DefaultValue
FROM sys.columns c
JOIN sys.tables t ON c.object_id = t.object_id
JOIN sys.schemas s ON t.schema_id = s.schema_id
JOIN sys.types ty ON c.user_type_id = ty.user_type_id
LEFT JOIN sys.index_columns ic ON ic.object_id = c.object_id AND ic.column_id = c.column_id
LEFT JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id AND i.is_primary_key = 1
LEFT JOIN sys.default_constraints dc ON c.default_object_id = dc.object_id
WHERE s.name = @schemaName AND t.name = @tableName AND c.name = @columnName;";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schemaName", schemaName);
                command.Parameters.AddWithValue("@tableName", tableName);
                command.Parameters.AddWithValue("@columnName", columnName);

                using (var reader = command.ExecuteReader(CommandBehavior.SingleRow))
                {
                    if (!reader.Read())
                    {
                        return null;
                    }

                    return ReadColumnInfo(reader);
                }
            }
        }

        private static IReadOnlyList<ColumnInfo> LoadColumns(clData data, string tableName)
        {
            using (var connection = data.GetConnection())
            {
                var schemaName = GetTableSchema(connection, tableName);
                var result = new List<ColumnInfo>();

                const string sql = @"SELECT
    c.name AS ColumnName,
    ty.name AS DataType,
    c.max_length,
    c.precision,
    c.scale,
    c.is_nullable,
    c.is_identity,
    CASE WHEN i.is_primary_key = 1 THEN 1 ELSE 0 END AS IsPrimaryKey,
    dc.definition AS DefaultValue
FROM sys.columns c
JOIN sys.tables t ON c.object_id = t.object_id
JOIN sys.schemas s ON t.schema_id = s.schema_id
JOIN sys.types ty ON c.user_type_id = ty.user_type_id
LEFT JOIN sys.index_columns ic ON ic.object_id = c.object_id AND ic.column_id = c.column_id
LEFT JOIN sys.indexes i ON ic.object_id = i.object_id AND ic.index_id = i.index_id AND i.is_primary_key = 1
LEFT JOIN sys.default_constraints dc ON c.default_object_id = dc.object_id
WHERE s.name = @schemaName AND t.name = @tableName
ORDER BY c.column_id;";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@schemaName", schemaName);
                    command.Parameters.AddWithValue("@tableName", tableName);

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            result.Add(ReadColumnInfo(reader));
                        }
                    }
                }

                return result.Count == 0 ? (IReadOnlyList<ColumnInfo>)Array.Empty<ColumnInfo>() : result.AsReadOnly();
            }
        }

        private static ColumnInfo ReadColumnInfo(SqlDataReader reader)
        {
            var dataType = reader.GetString(reader.GetOrdinal("DataType"));
            var maxLengthOrdinal = reader.GetOrdinal("max_length");
            var precisionOrdinal = reader.GetOrdinal("precision");
            var scaleOrdinal = reader.GetOrdinal("scale");
            var defaultOrdinal = reader.GetOrdinal("DefaultValue");

            var rawLength = reader.IsDBNull(maxLengthOrdinal) ? (int?)null : Convert.ToInt32(reader.GetValue(maxLengthOrdinal));
            var rawPrecision = reader.IsDBNull(precisionOrdinal) ? (int?)null : Convert.ToInt32(reader.GetValue(precisionOrdinal));
            var rawScale = reader.IsDBNull(scaleOrdinal) ? (int?)null : Convert.ToInt32(reader.GetValue(scaleOrdinal));
            var defaultValue = reader.IsDBNull(defaultOrdinal) ? null : reader.GetString(defaultOrdinal);

            return new ColumnInfo
            {
                Name = reader.GetString(reader.GetOrdinal("ColumnName")),
                DataType = dataType,
                Length = ConvertMaxLength(dataType, rawLength),
                Precision = rawPrecision,
                Scale = rawScale,
                IsNullable = reader.GetBoolean(reader.GetOrdinal("is_nullable")),
                IsIdentity = reader.GetBoolean(reader.GetOrdinal("is_identity")),
                IsPrimaryKey = reader.GetInt32(reader.GetOrdinal("IsPrimaryKey")) == 1,
                DefaultValue = NormalizeDefaultValue(defaultValue)
            };
        }

        private static int? ConvertMaxLength(string dataType, int? rawLength)
        {
            if (!rawLength.HasValue)
            {
                return null;
            }

            if (rawLength.Value == -1)
            {
                return -1;
            }

            switch (dataType.ToLowerInvariant())
            {
                case "nchar":
                case "nvarchar":
                    return rawLength.Value / 2;
                default:
                    return rawLength;
            }
        }

        private static string NormalizeDefaultValue(string definition)
        {
            if (string.IsNullOrWhiteSpace(definition))
            {
                return null;
            }

            var value = definition.Trim();
            while (value.StartsWith("(", StringComparison.Ordinal) && value.EndsWith(")", StringComparison.Ordinal) && value.Length > 1)
            {
                value = value.Substring(1, value.Length - 2).Trim();
            }

            return value;
        }

        private static bool IsIdentityType(string dataType)
        {
            if (string.IsNullOrWhiteSpace(dataType))
            {
                return false;
            }

            var normalized = dataType.Trim().ToLowerInvariant();
            return normalized == "int" || normalized == "bigint" || normalized == "decimal" || normalized == "numeric";
        }

        private static string BuildColumnDefinition(TableColumnInput column, bool isNullable, bool includeIdentity)
        {
            var sqlType = BuildSqlType(column.DataType, column.Length, column.Precision, column.Scale);
            var builder = new StringBuilder(sqlType);

            if (includeIdentity)
            {
                builder.Append(" IDENTITY(1,1)");
            }

            builder.Append(isNullable ? " NULL" : " NOT NULL");

            return builder.ToString();
        }

        private static string BuildColumnDefinition(string dataType, int? length, int? precision, int? scale, bool isNullable, bool includeIdentity)
        {
            var sqlType = BuildSqlType(dataType, length, precision, scale);
            var builder = new StringBuilder(sqlType);

            if (includeIdentity)
            {
                builder.Append(" IDENTITY(1,1)");
            }

            builder.Append(isNullable ? " NULL" : " NOT NULL");

            return builder.ToString();
        }

        private static string BuildSqlType(string dataType, int? length, int? precision, int? scale)
        {
            if (string.IsNullOrWhiteSpace(dataType))
            {
                throw new InvalidOperationException("Column data type is required.");
            }

            var normalized = dataType.Trim().ToLowerInvariant();
            switch (normalized)
            {
                case "char":
                case "nchar":
                case "varchar":
                case "nvarchar":
                case "binary":
                case "varbinary":
                    if (!length.HasValue)
                    {
                        throw new InvalidOperationException($"Column length is required for data type '{normalized}'.");
                    }

                    var effectiveLength = length.Value == -1 ? "MAX" : length.Value.ToString();
                    return $"{normalized.ToUpperInvariant()}({effectiveLength})";
                case "decimal":
                case "numeric":
                    var effectivePrecision = precision ?? throw new InvalidOperationException($"Precision is required for data type '{normalized}'.");
                    var effectiveScale = scale ?? 0;
                    return $"{normalized.ToUpperInvariant()}({effectivePrecision},{effectiveScale})";
                case "datetime2":
                case "datetimeoffset":
                case "time":
                    if (scale.HasValue)
                    {
                        return $"{normalized.ToUpperInvariant()}({scale.Value})";
                    }

                    break;
            }

            return normalized.ToUpperInvariant();
        }

        private static void ValidateColumnDefinition(TableColumnInput column, bool requireDataType)
        {
            if (requireDataType && string.IsNullOrWhiteSpace(column.DataType))
            {
                throw new InvalidOperationException($"Column '{column.Name}' must specify a data type.");
            }
        }

        private static string BuildCreateTableCommand(TableMetadata metadata)
        {
            var name = EscapeSqlLiteral(metadata.Name);
            var label = EscapeSqlLiteral(metadata.Label);
            var labelPlural = EscapeSqlLiteral(metadata.LabelPlural);

            var createFileLabels = metadata.CreateFileLabels ? 1 : 0;
            var createLinks = metadata.CreateLinks ? 1 : 0;
            var createFieldLabels = metadata.CreateFieldLabels ? 1 : 0;
            var createForm = metadata.CreateForm ? 1 : 0;
            var createDesktop = metadata.CreateDesktop ? 1 : 0;
            var createPermissions = metadata.CreatePermissions ? 1 : 0;

            return $"exec pCreateTable @par_sTableName='{name}',@par_sTableLabel='{label}',@par_sTableLabelPlural='{labelPlural}'," +
                   $"@par_bCreateFileLabels={createFileLabels},@par_bCreateLinks={createLinks},@par_bCreateFieldLabels={createFieldLabels}," +
                   $"@par_bCreateForm={createForm},@par_bCreateDesktop={createDesktop},@par_bCreatePermissions={createPermissions},@par_bVerbose=0";
        }

        private static void EnsureLabelUniqueness(IEnumerable<TableDescriptor> existingTables, TableMetadata metadata)
        {
            if (existingTables.Any(table => string.Equals(table.Label, metadata.Label, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"Table label '{metadata.Label}' already exists.");
            }

            if (existingTables.Any(table => string.Equals(table.LabelPlural, metadata.LabelPlural, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"Table plural label '{metadata.LabelPlural}' already exists.");
            }
        }

        private static string NormalizeIdentifier(string value, string fieldName)
        {
            if (string.IsNullOrWhiteSpace(value))
            {
                throw new ArgumentException($"{fieldName} is required.", fieldName);
            }

            var trimmed = value.Trim();
            if (!IdentifierRegex.IsMatch(trimmed))
            {
                throw new ArgumentException($"{fieldName} must contain only letters, numbers, or underscores.", fieldName);
            }

            return trimmed;
        }

        private static string EscapeSqlLiteral(string value)
        {
            return (value ?? string.Empty).Replace("'", "''");
        }

        private static string EscapeIdentifier(string identifier)
        {
            return "[" + identifier.Replace("]", "]]" ) + "]";
        }

        private static string ComposeFullTableName(string schemaName, string tableName)
        {
            return $"{EscapeIdentifier(schemaName)}.{EscapeIdentifier(tableName)}";
        }

        private static string BuildConstraintName(string baseName)
        {
            var sanitized = IdentifierRegex.IsMatch(baseName) ? baseName : new string(baseName.Where(char.IsLetterOrDigit).ToArray());
            if (sanitized.Length == 0)
            {
                sanitized = "DF_Auto";
            }

            return sanitized.Length <= 120 ? sanitized : sanitized.Substring(0, 120);
        }

        private static clData ResolveLegacyDataContext()
        {
            var data = Util.GetInstance("data") as clData;
            if (data == null)
            {
                throw new InvalidOperationException("Selltis legacy data context could not be resolved.");
            }

            return data;
        }

        private static IEnumerable<TableDescriptor> LoadSelltisTables(clData data, bool includeLabels)
        {
            var results = new List<TableDescriptor>();
            var files = data.GetFiles(false);
            if (files == null)
            {
                return results;
            }

            for (int index = 1; index <= files.GetDimension(); index++)
            {
                var tableName = files.GetItem(index);
                var descriptor = new TableDescriptor
                {
                    Name = tableName
                };

                if (includeLabels)
                {
                    descriptor.Label = data.GetFileLabelFromName(tableName);
                    descriptor.LabelPlural = data.GetFileLabelPlurFrName(tableName);
                }

                results.Add(descriptor);
            }

            return results;
        }

        private static TableInfo BuildTableInfo(clData data, string hostName, string tableName, bool includeColumns)
        {
            var label = data.GetFileLabelFromName(tableName);
            var labelPlural = data.GetFileLabelPlurFrName(tableName);

            IReadOnlyList<ColumnInfo> columns = Array.Empty<ColumnInfo>();
            if (includeColumns)
            {
                columns = LoadColumns(data, tableName);
            }

            return new TableInfo
            {
                HostName = hostName,
                Name = tableName,
                Label = label,
                LabelPlural = string.IsNullOrWhiteSpace(labelPlural) ? label : labelPlural,
                Columns = columns
            };
        }

        private static string GetTableSchema(SqlConnection connection, string tableName)
        {
            const string sql = @"SELECT TOP 1 TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @tableName;";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@tableName", tableName);

                var result = command.ExecuteScalar();
                return result as string ?? DefaultSchemaName;
            }
        }

        private sealed class TableDescriptor
        {
            public string Name { get; set; }
            public string Label { get; set; }
            public string LabelPlural { get; set; }
        }
    }
}
        private static void ValidateMutationInput(TableMutationInput input)
        {
            if (string.IsNullOrWhiteSpace(input.HostName))
            {
                throw new ArgumentException("Host name is required.", nameof(input));
            }

            if (input.Metadata == null)
            {
                throw new ArgumentException("Mutation metadata is required.", nameof(input));
            }

            if (string.IsNullOrWhiteSpace(input.Metadata.Name))
            {
                throw new ArgumentException("Table name is required within metadata.", nameof(input));
            }

            if (string.IsNullOrWhiteSpace(input.Metadata.Label))
            {
                throw new ArgumentException("Table label is required within metadata.", nameof(input));
            }

            if (string.IsNullOrWhiteSpace(input.Metadata.LabelPlural))
            {
                throw new ArgumentException("Table label plural is required within metadata.", nameof(input));
            }

            if (input.Columns == null)
            {
                return;
            }

            var names = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            for (int index = 0; index < input.Columns.Count; index++)
            {
                var column = input.Columns[index] ?? throw new ArgumentException($"Column at index {index} cannot be null.", nameof(input));

                if (string.IsNullOrWhiteSpace(column.Name))
                {
                    throw new ArgumentException($"Column name is required at index {index}.", nameof(input));
                }

                var normalized = NormalizeIdentifier(column.Name, $"Column[{index}]");
                if (!names.Add(normalized))
                {
                    throw new ArgumentException($"Column '{normalized}' appears multiple times in the payload.", nameof(input));
                }

                if (input.Operation == TableOperation.Create && column.Action == ColumnAction.Modify)
                {
                    throw new ArgumentException($"Column '{normalized}' cannot use MODIFY during table creation.", nameof(input));
                }
            }
        }

        private static string BuildSuccessMessage(TableOperation operation, string tableName)
        {
            return operation == TableOperation.Create
                ? $"Table '{tableName}' was created successfully."
                : $"Table '{tableName}' was altered successfully.";
        }
        private static clData ResolveLegacyDataContext()
        {
            var context = Util.GetInstance("data") as clData;
            if (context == null)
            {
                throw new InvalidOperationException("Selltis legacy data context could not be resolved.");
            }

            return context;
        }

        private static string NormalizeIdentifier(string identifier, string fieldName)
        {
            if (string.IsNullOrWhiteSpace(identifier))
            {
                throw new ArgumentException($"{fieldName} is required.", fieldName);
            }

            var trimmed = identifier.Trim();
            if (!IdentifierRegex.IsMatch(trimmed))
            {
                throw new ArgumentException($"{fieldName} must contain only letters, numbers, or underscores.", fieldName);
            }

            return trimmed;
        }

        private static void EnsureUniqueLabels(IEnumerable<TableMetadata> existing, TableMetadata incoming)
        {
            if (existing.Any(item => string.Equals(item.Label, incoming.Label, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"Table label '{incoming.Label}' already exists.");
            }

            if (existing.Any(item => string.Equals(item.LabelPlural, incoming.LabelPlural, StringComparison.OrdinalIgnoreCase)))
            {
                throw new InvalidOperationException($"Table plural label '{incoming.LabelPlural}' already exists.");
            }
        }

        private static void RunCreateTable(clData data, TableMetadata metadata)
        {
            clCache.ClearCache();

            var sql = BuildCreateTableCommand(metadata);
            if (!data.RunSQLQuery(sql))
            {
                throw new InvalidOperationException("Legacy runtime failed to create the table.");
            }
        }
        private static IDictionary<string, TableMetadata> LoadTableMetadata(clData data)
        {
            var results = new Dictionary<string, TableMetadata>(StringComparer.OrdinalIgnoreCase);
            var files = data.GetFiles(false);
            if (files == null)
            {
                return results;
            }

            for (int index = 1; index <= files.GetDimension(); index++)
            {
                var name = files.GetItem(index);
                var label = data.GetFileLabelFromName(name);
                var labelPlural = data.GetFileLabelPlurFrName(name);
                results[name] = new TableMetadata
                {
                    Name = name,
                    Label = label,
                    LabelPlural = labelPlural
                };
            }

            return results;
        }
        private IReadOnlyList<string> ProcessColumns(clData data, string tableName, IReadOnlyList<TableColumnInput> columns)
        {
            var warnings = new List<string>();

            using (var connection = data.GetConnection())
            {
                var schema = GetTableSchema(connection, tableName);

                foreach (var column in columns)
                {
                    var normalized = NormalizeIdentifier(column.Name, "Column name");
                    var action = column.Action;

                    if (action == ColumnAction.Add && ColumnExists(connection, schema, tableName, normalized))
                    {
                        warnings.Add($"Column '{normalized}' already exists on table '{tableName}'.");
                        continue;
                    }

                    if (action == ColumnAction.Modify && !ColumnExists(connection, schema, tableName, normalized))
                    {
                        warnings.Add($"Column '{normalized}' does not exist on table '{tableName}'.");
                        continue;
                    }

                    switch (action)
                    {
                        case ColumnAction.Add:
                            AddColumn(connection, schema, tableName, normalized, column);
                            break;
                        case ColumnAction.Modify:
                            warnings.AddRange(AlterColumn(connection, schema, tableName, normalized, column));
                            break;
                        default:
                            throw new InvalidOperationException($"Column action '{action}' is not supported.");
                    }
                }
            }

            return warnings.Count == 0 ? (IReadOnlyList<string>)Array.Empty<string>() : warnings.AsReadOnly();
        }
        private static string GetTableSchema(SqlConnection connection, string tableName)
        {
            const string sql = "SELECT TOP 1 TABLE_SCHEMA FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = @name";
            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@name", tableName);
                var result = command.ExecuteScalar();
                return result as string ?? DefaultSchema;
            }
        }

        private static bool ColumnExists(SqlConnection connection, string schema, string tableName, string columnName)
        {
            const string sql = @"SELECT 1
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = @schema AND TABLE_NAME = @table AND COLUMN_NAME = @column";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schema", schema);
                command.Parameters.AddWithValue("@table", tableName);
                command.Parameters.AddWithValue("@column", columnName);

                using (var reader = command.ExecuteReader())
                {
                    return reader.Read();
                }
            }
        }
        private static ColumnInfo GetColumnMetadata(SqlConnection connection, string schema, string tableName, string columnName)
        {
            const string sql = @"SELECT
    c.name AS ColumnName,
    t.name AS TableName,
    ty.name AS DataType,
    c.max_length,
    c.precision,
    c.scale,
    c.is_nullable,
    c.is_identity,
    CASE WHEN pk.is_primary_key = 1 THEN 1 ELSE 0 END AS IsPrimaryKey,
    dc.definition AS DefaultValue
FROM sys.columns c
JOIN sys.tables t ON c.object_id = t.object_id
JOIN sys.schemas s ON t.schema_id = s.schema_id
JOIN sys.types ty ON c.user_type_id = ty.user_type_id
LEFT JOIN sys.index_columns ic ON ic.object_id = c.object_id AND ic.column_id = c.column_id
LEFT JOIN sys.indexes pk ON pk.object_id = ic.object_id AND pk.index_id = ic.index_id AND pk.is_primary_key = 1
LEFT JOIN sys.default_constraints dc ON c.default_object_id = dc.object_id
WHERE s.name = @schema AND t.name = @table AND c.name = @column";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schema", schema);
                command.Parameters.AddWithValue("@table", tableName);
                command.Parameters.AddWithValue("@column", columnName);

                using (var reader = command.ExecuteReader(CommandBehavior.SingleRow))
                {
                    if (!reader.Read())
                    {
                        return null;
                    }

                    return new ColumnInfo
                    {
                        Name = reader.GetString(reader.GetOrdinal("ColumnName")),
                        DataType = reader.GetString(reader.GetOrdinal("DataType")),
                        Length = ConvertLength(reader, "max_length", reader.GetString(reader.GetOrdinal("DataType"))),
                        Precision = reader.IsDBNull(reader.GetOrdinal("precision")) ? (int?)null : Convert.ToInt32(reader.GetValue(reader.GetOrdinal("precision"))),
                        Scale = reader.IsDBNull(reader.GetOrdinal("scale")) ? (int?)null : Convert.ToInt32(reader.GetValue(reader.GetOrdinal("scale"))),
                        IsNullable = reader.GetBoolean(reader.GetOrdinal("is_nullable")),
                        IsIdentity = reader.GetBoolean(reader.GetOrdinal("is_identity")),
                        IsPrimaryKey = reader.GetInt32(reader.GetOrdinal("IsPrimaryKey")) == 1,
                        DefaultValue = NormalizeDefaultValue(reader.IsDBNull(reader.GetOrdinal("DefaultValue")) ? null : reader.GetString(reader.GetOrdinal("DefaultValue")))
                    };
                }
            }
        }

        private void AddColumn(SqlConnection connection, string schema, string tableName, string columnName, TableColumnInput column)
        {
            var sqlDefinition = BuildColumnDefinition(column);
            var statement = $"ALTER TABLE {ComposeFullTableName(schema, tableName)} ADD {EscapeIdentifier(columnName)} {sqlDefinition}";

            using (var command = new SqlCommand(statement, connection))
            {
                command.ExecuteNonQuery();
            }

            if (!string.IsNullOrWhiteSpace(column.DefaultValue))
            {
                ApplyDefaultConstraint(connection, schema, tableName, columnName, column.DefaultValue);
            }

            if (column.IsPrimaryKey)
            {
                if (column.Nullable.GetValueOrDefault(true))
                {
                    throw new InvalidOperationException($"Primary key column '{columnName}' cannot be nullable.");
                }

                EnsurePrimaryKey(connection, schema, tableName, new[] { columnName });
            }
        }

        private IReadOnlyList<string> AlterColumn(SqlConnection connection, string schema, string tableName, string columnName, TableColumnInput column)
        {
            var warnings = new List<string>();
            var existing = GetColumnMetadata(connection, schema, tableName, columnName) ?? throw new InvalidOperationException($"Metadata for column '{columnName}' could not be resolved.");

            var effectiveType = string.IsNullOrWhiteSpace(column.DataType) ? existing.DataType : column.DataType;
            var effectiveLength = column.Length ?? existing.Length;
            var effectivePrecision = column.Precision ?? existing.Precision;
            var effectiveScale = column.Scale ?? existing.Scale;
            var allowsNull = column.Nullable ?? existing.IsNullable;

            if (existing.IsIdentity && !column.IsIdentity.GetValueOrDefault(true))
            {
                warnings.Add($"Identity configuration on column '{columnName}' remains unchanged.");
            }

            if (!existing.IsIdentity && column.IsIdentity)
            {
                warnings.Add($"Adding IDENTITY to existing column '{columnName}' is not supported.");
            }

            if (existing.IsPrimaryKey && column.IsPrimaryKey == false)
            {
                warnings.Add($"Primary key membership for column '{columnName}' remains unchanged.");
            }

            if (!existing.IsPrimaryKey && column.IsPrimaryKey)
            {
                warnings.Add($"Adding column '{columnName}' to the primary key requires manual intervention.");
            }

            if (column.IsPrimaryKey && allowsNull)
            {
                throw new InvalidOperationException($"Primary key column '{columnName}' cannot be nullable.");
            }

            var definition = BuildColumnDefinition(effectiveType, effectiveLength, effectivePrecision, effectiveScale, allowsNull, includeIdentity: false);
            var statement = $"ALTER TABLE {ComposeFullTableName(schema, tableName)} ALTER COLUMN {EscapeIdentifier(columnName)} {definition}";

            using (var command = new SqlCommand(statement, connection))
            {
                command.ExecuteNonQuery();
            }

            if (column.DefaultValue != null)
            {
                DropDefaultConstraint(connection, schema, tableName, columnName);
                if (!string.IsNullOrWhiteSpace(column.DefaultValue))
                {
                    ApplyDefaultConstraint(connection, schema, tableName, columnName, column.DefaultValue);
                }
            }

            return warnings.Count == 0 ? (IReadOnlyList<string>)Array.Empty<string>() : warnings.AsReadOnly();
        }
        private static string BuildColumnDefinition(TableColumnInput column)
        {
            var sqlType = BuildSqlType(column.DataType, column.Length, column.Precision, column.Scale);
            var builder = new StringBuilder(sqlType);

            if (column.IsIdentity)
            {
                builder.Append(" IDENTITY(1,1)");
            }

            var allowsNull = column.Nullable.GetValueOrDefault(true);
            builder.Append(allowsNull ? " NULL" : " NOT NULL");
            return builder.ToString();
        }

        private static string BuildColumnDefinition(string dataType, int? length, int? precision, int? scale, bool allowsNull, bool includeIdentity)
        {
            var sqlType = BuildSqlType(dataType, length, precision, scale);
            var builder = new StringBuilder(sqlType);
            if (includeIdentity)
            {
                builder.Append(" IDENTITY(1,1)");
            }

            builder.Append(allowsNull ? " NULL" : " NOT NULL");
            return builder.ToString();
        }

        private static string BuildSqlType(string dataType, int? length, int? precision, int? scale)
        {
            if (string.IsNullOrWhiteSpace(dataType))
            {
                throw new InvalidOperationException("Column data type is required.");
            }

            var normalized = dataType.Trim().ToLowerInvariant();
            switch (normalized)
            {
                case "char":
                case "nchar":
                case "varchar":
                case "nvarchar":
                case "binary":
                case "varbinary":
                    if (!length.HasValue)
                    {
                        throw new InvalidOperationException($"Column length is required for data type '{normalized}'.");
                    }

                    var effectiveLength = length.Value == -1 ? "MAX" : length.Value.ToString();
                    return $"{normalized.ToUpperInvariant()}({effectiveLength})";
                case "decimal":
                case "numeric":
                    var effectivePrecision = precision ?? throw new InvalidOperationException($"Precision is required for data type '{normalized}'.");
                    var effectiveScale = scale ?? 0;
                    return $"{normalized.ToUpperInvariant()}({effectivePrecision},{effectiveScale})";
                case "datetime2":
                case "datetimeoffset":
                case "time":
                    if (scale.HasValue)
                    {
                        return $"{normalized.ToUpperInvariant()}({scale.Value})";
                    }

                    break;
            }

            return normalized.ToUpperInvariant();
        }
        private static void ApplyDefaultConstraint(SqlConnection connection, string schema, string tableName, string columnName, string defaultValue)
        {
            var constraintName = BuildConstraintName($"DF_{tableName}_{columnName}");
            var statement = $"ALTER TABLE {ComposeFullTableName(schema, tableName)} ADD CONSTRAINT {EscapeIdentifier(constraintName)} DEFAULT ({defaultValue}) FOR {EscapeIdentifier(columnName)}";

            using (var command = new SqlCommand(statement, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static void DropDefaultConstraint(SqlConnection connection, string schema, string tableName, string columnName)
        {
            const string sql = @"SELECT dc.name
FROM sys.default_constraints dc
JOIN sys.columns c ON c.default_object_id = dc.object_id
JOIN sys.tables t ON t.object_id = c.object_id
JOIN sys.schemas s ON s.schema_id = t.schema_id
WHERE s.name = @schema AND t.name = @table AND c.name = @column";

            string constraintName = null;
            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schema", schema);
                command.Parameters.AddWithValue("@table", tableName);
                command.Parameters.AddWithValue("@column", columnName);
                var result = command.ExecuteScalar();
                if (result != null && result != DBNull.Value)
                {
                    constraintName = Convert.ToString(result);
                }
            }

            if (string.IsNullOrWhiteSpace(constraintName))
            {
                return;
            }

            var statement = $"ALTER TABLE {ComposeFullTableName(schema, tableName)} DROP CONSTRAINT {EscapeIdentifier(constraintName)}";
            using (var command = new SqlCommand(statement, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static void EnsurePrimaryKey(SqlConnection connection, string schema, string tableName, IEnumerable<string> columnNames)
        {
            if (HasPrimaryKey(connection, schema, tableName))
            {
                throw new InvalidOperationException($"Table '{tableName}' already has a primary key.");
            }

            var constraintName = BuildConstraintName($"PK_{tableName}");
            var columns = string.Join(",", columnNames.Select(EscapeIdentifier));
            var statement = $"ALTER TABLE {ComposeFullTableName(schema, tableName)} ADD CONSTRAINT {EscapeIdentifier(constraintName)} PRIMARY KEY ({columns})";

            using (var command = new SqlCommand(statement, connection))
            {
                command.ExecuteNonQuery();
            }
        }

        private static bool HasPrimaryKey(SqlConnection connection, string schema, string tableName)
        {
            const string sql = @"SELECT 1
FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS
WHERE TABLE_SCHEMA = @schema AND TABLE_NAME = @table AND CONSTRAINT_TYPE = 'PRIMARY KEY'";

            using (var command = new SqlCommand(sql, connection))
            {
                command.Parameters.AddWithValue("@schema", schema);
                command.Parameters.AddWithValue("@table", tableName);

                using (var reader = command.ExecuteReader())
                {
                    return reader.Read();
                }
            }
        }
        private static IReadOnlyList<ColumnInfo> LoadColumns(clData data, string tableName)
        {
            using (var connection = data.GetConnection())
            {
                var schema = GetTableSchema(connection, tableName);
                var columns = new List<ColumnInfo>();
                const string sql = @"SELECT
    c.name AS ColumnName,
    ty.name AS DataType,
    c.max_length,
    c.precision,
    c.scale,
    c.is_nullable,
    c.is_identity,
    CASE WHEN pk.is_primary_key = 1 THEN 1 ELSE 0 END AS IsPrimaryKey,
    dc.definition AS DefaultValue
FROM sys.columns c
JOIN sys.tables t ON c.object_id = t.object_id
JOIN sys.schemas s ON t.schema_id = s.schema_id
JOIN sys.types ty ON c.user_type_id = ty.user_type_id
LEFT JOIN sys.index_columns ic ON ic.object_id = c.object_id AND ic.column_id = c.column_id
LEFT JOIN sys.indexes pk ON pk.object_id = ic.object_id AND pk.index_id = ic.index_id AND pk.is_primary_key = 1
LEFT JOIN sys.default_constraints dc ON c.default_object_id = dc.object_id
WHERE s.name = @schema AND t.name = @table
ORDER BY c.column_id";

                using (var command = new SqlCommand(sql, connection))
                {
                    command.Parameters.AddWithValue("@schema", schema);
                    command.Parameters.AddWithValue("@table", tableName);

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var typeName = reader.GetString(reader.GetOrdinal("DataType"));
                            columns.Add(new ColumnInfo
                            {
                                Name = reader.GetString(reader.GetOrdinal("ColumnName")),
                                DataType = typeName,
                                Length = ConvertLength(reader, "max_length", typeName),
                                Precision = reader.IsDBNull(reader.GetOrdinal("precision")) ? (int?)null : Convert.ToInt32(reader.GetValue(reader.GetOrdinal("precision"))),
                                Scale = reader.IsDBNull(reader.GetOrdinal("scale")) ? (int?)null : Convert.ToInt32(reader.GetValue(reader.GetOrdinal("scale"))),
                                IsNullable = reader.GetBoolean(reader.GetOrdinal("is_nullable")),
                                IsIdentity = reader.GetBoolean(reader.GetOrdinal("is_identity")),
                                IsPrimaryKey = reader.GetInt32(reader.GetOrdinal("IsPrimaryKey")) == 1,
                                DefaultValue = NormalizeDefaultValue(reader.IsDBNull(reader.GetOrdinal("DefaultValue")) ? null : reader.GetString(reader.GetOrdinal("DefaultValue")))
                            });
                        }
                    }
                }

                return columns.Count == 0 ? (IReadOnlyList<ColumnInfo>)Array.Empty<ColumnInfo>() : columns.AsReadOnly();
            }
        }

        private static TableInfo BuildTableInfo(clData data, string hostName, string tableName, bool includeColumns)
        {
            var label = data.GetFileLabelFromName(tableName);
            var labelPlural = data.GetFileLabelPlurFrName(tableName);
            var columns = includeColumns ? LoadColumns(data, tableName) : Array.Empty<ColumnInfo>();

            return new TableInfo
            {
                HostName = hostName,
                Name = tableName,
                Label = label,
                LabelPlural = string.IsNullOrWhiteSpace(labelPlural) ? label : labelPlural,
                Columns = columns
            };
        }

        private static string NormalizeDefaultValue(string definition)
        {
            if (string.IsNullOrWhiteSpace(definition))
            {
                return null;
            }

            var value = definition.Trim();
            while (value.StartsWith("(", StringComparison.Ordinal) && value.EndsWith(")", StringComparison.Ordinal) && value.Length > 1)
            {
                value = value.Substring(1, value.Length - 2).Trim();
            }

            return value;
        }

        private static int? ConvertLength(SqlDataReader reader, string fieldName, string dataType)
        {
            var ordinal = reader.GetOrdinal(fieldName);
            if (reader.IsDBNull(ordinal))
            {
                return null;
            }

            var raw = Convert.ToInt32(reader.GetValue(ordinal));
            if (raw == -1)
            {
                return -1;
            }

            switch (dataType.ToLowerInvariant())
            {
                case "nchar":
                case "nvarchar":
                    return raw / 2;
                default:
                    return raw;
            }
        }

        private static string ComposeFullTableName(string schema, string tableName)
        {
            return $"{EscapeIdentifier(schema)}.{EscapeIdentifier(tableName)}";
        }

        private static string EscapeIdentifier(string identifier)
        {
            return "[" + identifier.Replace("]", "]]" ) + "]";
        }

        private static string BuildConstraintName(string baseName)
        {
            var sanitized = IdentifierRegex.IsMatch(baseName) ? baseName : new string(baseName.Where(char.IsLetterOrDigit).ToArray());
            if (sanitized.Length == 0)
            {
                sanitized = "DF_Auto";
            }

            return sanitized.Length <= 120 ? sanitized : sanitized.Substring(0, 120);
        }

        private static string BuildCreateTableCommand(TableMetadata metadata)
        {
            var name = EscapeSqlLiteral(metadata.Name);
            var label = EscapeSqlLiteral(metadata.Label);
            var plural = EscapeSqlLiteral(metadata.LabelPlural);

            var createFileLabels = metadata.CreateFileLabels ? 1 : 0;
            var createLinks = metadata.CreateLinks ? 1 : 0;
            var createFieldLabels = metadata.CreateFieldLabels ? 1 : 0;
            var createForm = metadata.CreateForm ? 1 : 0;
            var createDesktop = metadata.CreateDesktop ? 1 : 0;
            var createPermissions = metadata.CreatePermissions ? 1 : 0;

            return $"exec pCreateTable @par_sTableName='{name}',@par_sTableLabel='{label}',@par_sTableLabelPlural='{plural}'," +
                   $"@par_bCreateFileLabels={createFileLabels},@par_bCreateLinks={createLinks},@par_bCreateFieldLabels={createFieldLabels}," +
                   $"@par_bCreateForm={createForm},@par_bCreateDesktop={createDesktop},@par_bCreatePermissions={createPermissions},@par_bVerbose=0";
        }

        private static string EscapeSqlLiteral(string value)
        {
            return (value ?? string.Empty).Replace("'", "''");
        }
    }
}
